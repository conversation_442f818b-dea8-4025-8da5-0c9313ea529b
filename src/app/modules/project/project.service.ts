import { Prisma } from "@prisma/client";
import { prisma } from "../../config/db";

const generateSlug = (title: string) => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/\s+/g, "-")
    .replace(/-+/g, "-");
};
const createProject = async (data: Prisma.ProjectCreateInput) => {
  if (!data.title) {
    throw new Error("Title is required");
  }
  let slug = generateSlug(data.title);

  const isExisting = await prisma.project.findUnique({
    where: {
      slug,
    },
  });
  if (isExisting) {
    slug = `${slug}-${Date.now()}`;
  }
  const result = await prisma.project.create({
    data: {
      ...data,
      slug,
    },
  });
  return result;
};

const getProjects = async ({
  page = 1,
  limit = 10,
  search,
  sortBy = "createdAt",
  sortOrder = "desc",
}: {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}) => {
  const skip = (page - 1) * limit;

  // Build the where clause with proper filtering
  const whereConditions: any[] = [];

  if (search && search.trim()) {
    whereConditions.push({
      OR: [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { type: { contains: search, mode: "insensitive" } },
      ],
    });
  }

  const where =
    whereConditions.length > 0 ? { AND: whereConditions } : undefined;

  const total = await prisma.project.count({ where });

  const projects = await prisma.project.findMany({
    where,
    skip,
    take: limit,
    orderBy: {
      [sortBy]: sortOrder,
    },
  });

  return {
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: projects,
  };
};

const getProjectById = async (id: number | string) => {
  const result = await prisma.project.findUnique({
    where: {
      ...(typeof id === "number" ? { id } : { slug: id }),
    },
  });
  if (!result) {
    throw new Error("Project not found");
  }
  return result;
};

const updateProject = async (id: number, data: Prisma.ProjectUpdateInput) => {
  if (!data.title) {
    throw new Error("Title is required");
  }
  let slug = generateSlug(data.title as string);

  const isExisting = await prisma.project.findUnique({
    where: {
      slug,
    },
  });
  if (isExisting) {
    slug = `${slug}-${Date.now()}`;
  }
  data.slug = slug;

  const result = await prisma.project.update({
    where: {
      id,
    },
    data,
  });
  return result;
};

const deleteProject = async (id: number) => {
  const result = await prisma.project.delete({
    where: {
      id,
    },
  });
  return result;
};

export const projectService = {
  createProject,
  getProjects,
  getProjectById,
  updateProject,
  deleteProject,
};
