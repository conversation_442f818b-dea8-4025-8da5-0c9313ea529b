{"name": "next-blog", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "prisma:push": "prisma db push", "prisma:pull": "prisma db pull", "prisma:studio": "prisma studio", "prisma:seed": "prisma db seed", "prisma:reset": "prisma migrate reset", "prisma:migrate": "prisma migrate dev", "prisma:generate": "prisma generate"}, "keywords": [], "author": "Next Level Web Development", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@prisma/client": "^6.16.1", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "compression": "^1.8.1", "cors": "^2.8.5", "express": "^5.1.0", "http": "^0.0.1-security", "ts-node-dev": "^2.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.2.0", "dotenv": "^17.2.2", "prisma": "^6.16.1", "tsx": "^4.20.5", "typescript": "^5.9.2"}}